using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AIMemoryReader
{
    internal static class NativeMethods
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        internal static extern bool VirtualProtectEx(IntPtr hProcess, IntPtr lpAddress, UIntPtr dwSize, uint flNewProtect, out uint lpflOldProtect);
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct MEMORY_BASIC_INFORMATION
    {
        public IntPtr BaseAddress;
        public IntPtr AllocationBase;
        public uint AllocationProtect;
        public IntPtr RegionSize;
        public uint State;
        public uint Protect;
        public uint Type;
    }

    public class Memory
    {
        // P/Invoke declarations
        const int PROCESS_VM_READ = 0x0010;
        const int PROCESS_VM_WRITE = 0x0020;
        const int PROCESS_VM_OPERATION = 0x0008;

        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesWritten);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool CloseHandle(IntPtr hObject);

        private IntPtr processHandle;
        private Process process;

        public Process Process
        {
            get { return process; }
        }

        public Memory(string processName)
        {
            Process[] processes = Process.GetProcessesByName(processName.Replace(".exe", ""));
            if (processes.Length > 0)
            {
                process = processes[0];
                processHandle = OpenProcess(PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION, false, process.Id);
                Assembly = new AssemblyExecutor(this);
            }
            else
            {
                throw new ArgumentException("Process not found");
            }
        }

        ~Memory()
        {
            if (processHandle != IntPtr.Zero)
            {
                CloseHandle(processHandle);
            }
        }

        public T Read<T>(IntPtr address) where T : struct
        {
            int size = Marshal.SizeOf(typeof(T));
            byte[] buffer = new byte[size];
            int bytesRead;
            ReadProcessMemory(processHandle, address, buffer, size, out bytesRead);

            GCHandle handle = GCHandle.Alloc(buffer, GCHandleType.Pinned);
            T data = (T)Marshal.PtrToStructure(handle.AddrOfPinnedObject(), typeof(T));
            handle.Free();

            return data;
        }

        public string ReadString(IntPtr address, int size, Encoding encoding)
        {
            byte[] buffer = new byte[size];
            int bytesRead;
            ReadProcessMemory(processHandle, address, buffer, size, out bytesRead);
            string text = encoding.GetString(buffer);
            if (text.Contains("\0"))
            {
                text = text.Substring(0, text.IndexOf('\0'));
            }
            return text;
        }

        public byte[] ReadBytes(IntPtr address, int count)
        {
            byte[] buffer = new byte[count];
            int bytesRead;
            ReadProcessMemory(processHandle, address, buffer, count, out bytesRead);
            return buffer;
        }

        public void Write<T>(IntPtr address, T value) where T : struct
        {
            int size = Marshal.SizeOf(typeof(T));
            byte[] buffer = new byte[size];

            IntPtr ptr = Marshal.AllocHGlobal(size);
            Marshal.StructureToPtr(value, ptr, true);
            Marshal.Copy(ptr, buffer, 0, size);
            Marshal.FreeHGlobal(ptr);

            uint oldProtect;
            NativeMethods.VirtualProtectEx(processHandle, address, (UIntPtr)size, 0x40, out oldProtect);
            int bytesWritten;
            if (!WriteProcessMemory(processHandle, address, buffer, size, out bytesWritten))
            {
                throw new System.ComponentModel.Win32Exception();
            }
            NativeMethods.VirtualProtectEx(processHandle, address, (UIntPtr)size, oldProtect, out oldProtect);
        }

        public void WriteString(IntPtr address, string text, Encoding encoding)
        {
            byte[] buffer = encoding.GetBytes(text + "\0");
            uint oldProtect;
            NativeMethods.VirtualProtectEx(processHandle, address, (UIntPtr)buffer.Length, 0x40, out oldProtect);
            int bytesWritten;
            if (!WriteProcessMemory(processHandle, address, buffer, buffer.Length, out bytesWritten))
            {
                throw new System.ComponentModel.Win32Exception();
            }
            NativeMethods.VirtualProtectEx(processHandle, address, (UIntPtr)buffer.Length, oldProtect, out oldProtect);
        }

        public void WriteBytes(IntPtr address, byte[] bytes)
        {
            uint oldProtect;
            NativeMethods.VirtualProtectEx(processHandle, address, (UIntPtr)bytes.Length, 0x40, out oldProtect);
            int bytesWritten;
            if (!WriteProcessMemory(processHandle, address, bytes, bytes.Length, out bytesWritten))
            {
                throw new System.ComponentModel.Win32Exception();
            }
            NativeMethods.VirtualProtectEx(processHandle, address, (UIntPtr)bytes.Length, oldProtect, out oldProtect);
        }

        private bool IsValidMemoryRegion(MEMORY_BASIC_INFORMATION memInfo)
        {
            const uint MEM_COMMIT = 0x1000;
            const uint PAGE_GUARD = 0x100;
            const uint PAGE_NOACCESS = 0x01;
            
            return memInfo.State == MEM_COMMIT && 
                   (memInfo.Protect & PAGE_GUARD) == 0 && 
                   memInfo.Protect != PAGE_NOACCESS;
        }

        private class MemoryRegion
        {
            public IntPtr BaseAddress { get; set; }
            public int Size { get; set; }
            public byte[] Data { get; set; }
        }

        private List<MemoryRegion> GetMemoryRegions(bool onlyWritable = true)
        {
            var regions = new List<MemoryRegion>();
            long maxAddress = 0x7FFFFFFF;
            long currentAddr = 0;

            while (currentAddr < maxAddress)
            {
                IntPtr currentAddress = new IntPtr(currentAddr);
                MEMORY_BASIC_INFORMATION memInfo;
                int result = VirtualQueryEx(processHandle, currentAddress, out memInfo, (uint)Marshal.SizeOf(typeof(MEMORY_BASIC_INFORMATION)));

                if (result == 0)
                    break;

                if (IsValidMemoryRegion(memInfo) && (!onlyWritable || (memInfo.Protect & 0x02) != 0)) // Check if writable when onlyWritable is true
                {
                    var region = new MemoryRegion
                    {
                        BaseAddress = memInfo.BaseAddress,
                        Size = (int)memInfo.RegionSize.ToInt64()
                    };
                    regions.Add(region);
                }

                currentAddr = memInfo.BaseAddress.ToInt64() + (long)memInfo.RegionSize;
            }

            return regions;
        }

        private int[] ComputeKMPTable(byte[] pattern)
        {
            int[] table = new int[pattern.Length];
            int pos = 1, cnd = 0;
            table[0] = -1;

            while (pos < pattern.Length)
            {
                if (pattern[pos] == pattern[cnd])
                    table[pos] = table[cnd];
                else
                {
                    table[pos] = cnd;
                    while (cnd >= 0 && pattern[pos] != pattern[cnd])
                        cnd = table[cnd];
                }
                pos++;
                cnd++;
            }
            return table;
        }

        private List<int> KMPSearch(byte[] data, byte[] pattern)
        {
            var matches = new List<int>();
            if (pattern.Length == 0) return matches;

            int[] table = ComputeKMPTable(pattern);
            int j = 0, k = 0;

            while (j < data.Length)
            {
                if (pattern[k] == data[j])
                {
                    j++; k++;
                    if (k == pattern.Length)
                    {
                        matches.Add(j - k);
                        k = table[k - 1];
                    }
                }
                else
                {
                    k = table[k];
                    if (k < 0)
                    {
                        j++;
                        k++;
                    }
                }
            }

            return matches;
        }

        public void FindAndReplaceString(string searchString, string replaceString, Encoding encoding)
        {
            // Try multiple encodings to ensure we find the string
            var encodings = new[] { Encoding.ASCII, Encoding.Unicode, Encoding.UTF8 };
            var searchPatterns = encodings.Select(enc => enc.GetBytes(searchString)).ToList();
            byte[] replaceBytes = encoding.GetBytes(replaceString);

            if (searchString.Length == 0)
                return;

            // Get all memory regions, including read-only ones
            var regions = GetMemoryRegions(onlyWritable: false);
            int totalMatches = 0;

            foreach (var region in regions)
            {
                try
                {
                    region.Data = new byte[region.Size];
                    int bytesRead;
                    if (ReadProcessMemory(processHandle, region.BaseAddress, region.Data, region.Size, out bytesRead))
                    {
                        foreach (var pattern in searchPatterns)
                        {
                            var matches = KMPSearch(region.Data, pattern);

                            foreach (var matchOffset in matches)
                            {
                                try
                                {
                                    IntPtr writeAddress = IntPtr.Add(region.BaseAddress, matchOffset);
                                    
                                    // Try to make memory writable temporarily
                                    uint oldProtect;
                                    if (NativeMethods.VirtualProtectEx(processHandle, writeAddress, (UIntPtr)replaceBytes.Length, 0x40, out oldProtect))
                                    {
                                        if (replaceBytes.Length <= pattern.Length)
                                        {
                                            byte[] paddedBytes = new byte[pattern.Length];
                                            Array.Copy(replaceBytes, paddedBytes, replaceBytes.Length);
                                            WriteBytes(writeAddress, paddedBytes);
                                        }
                                        else
                                        {
                                            WriteBytes(writeAddress, replaceBytes);
                                        }
                                        
                                        // Restore original protection
                                        NativeMethods.VirtualProtectEx(processHandle, writeAddress, (UIntPtr)replaceBytes.Length, oldProtect, out oldProtect);
                                        
                                        totalMatches++;
                                        MessageBox.Show($"Replaced '{searchString}' with '{replaceString}' at address 0x{writeAddress.ToInt64():X8}");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    // Skip if we can't write to this location
                                    continue;
                                }
                            }
                        }
                    }
                }
                catch
                {
                    // Skip regions that can't be read
                    continue;
                }
            }

            if (totalMatches == 0)
            {
                MessageBox.Show($"No occurrences of '{searchString}' were found in memory.");
            }
            else
            {
                MessageBox.Show($"Successfully replaced {totalMatches} occurrences of '{searchString}' with '{replaceString}'");
            }
        }

        [DllImport("kernel32.dll")]
        static extern int VirtualQueryEx(IntPtr hProcess, IntPtr lpAddress, out MEMORY_BASIC_INFORMATION lpBuffer, uint dwLength);

        /// <summary>
        /// Reads all values from a std::map by iterating through its internal structure
        /// </summary>
        /// <param name="mapAddress">Address of the std::map object</param>
        /// <param name="dataOffset">Offset from iterator to the actual data (usually 20 for MSVC std::map)</param>
        /// <param name="targetOffset">Offset within the data structure to get m_pTarget (map->second.m_pTarget)</param>
        /// <returns>Array of IntPtr values representing all m_pTarget values in the map</returns>
        public IntPtr[] ReadStdMapValues(IntPtr mapAddress, int dataOffset = 20, int targetOffset = 0)
        {
            var values = new List<IntPtr>();

            try
            {
                // Read the map structure to get the end iterator (first element of the map array)
                IntPtr endIterator = Read<IntPtr>(mapAddress);

                // Get the begin iterator by calling the equivalent of map.begin()
                // For MSVC std::map, this is typically at offset 4 from the map base
                IntPtr beginIteratorPtr = IntPtr.Add(mapAddress, 4);
                IntPtr currentIterator = Read<IntPtr>(beginIteratorPtr);

                // If the map is empty, begin == end
                if (currentIterator == endIterator)
                {
                    return values.ToArray();
                }

                // Iterate through the map nodes
                int maxIterations = 10000; // Safety limit to prevent infinite loops
                int iterations = 0;

                while (currentIterator != endIterator && iterations < maxIterations)
                {
                    try
                    {
                        // Get the data pointer (iterator + dataOffset)
                        IntPtr dataPtr = IntPtr.Add(currentIterator, dataOffset);
                        IntPtr actualDataPtr = Read<IntPtr>(dataPtr);

                        // Read the target value (map->second.m_pTarget)
                        IntPtr targetPtr = IntPtr.Add(actualDataPtr, targetOffset);
                        IntPtr targetValue = Read<IntPtr>(targetPtr);

                        values.Add(targetValue);

                        // Move to next iterator (equivalent of ++iterator)
                        // This simulates the sub_C10660(&v3) call from your IDA code
                        currentIterator = GetNextMapIterator(currentIterator);

                        iterations++;
                    }
                    catch (Exception)
                    {
                        // If we can't read a node, break the loop
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                // Return what we have so far if there's an error
                System.Windows.Forms.MessageBox.Show($"Error reading std::map: {ex.Message}");
            }

            return values.ToArray();
        }

        /// <summary>
        /// Reads all values from a std::map with custom data extraction
        /// </summary>
        /// <param name="mapAddress">Address of the std::map object</param>
        /// <param name="dataOffset">Offset from iterator to the actual data</param>
        /// <param name="extractValue">Function to extract the desired value from the data pointer</param>
        /// <returns>Array of extracted values</returns>
        public T[] ReadStdMapValues<T>(IntPtr mapAddress, int dataOffset, Func<IntPtr, T> extractValue) where T : struct
        {
            var values = new List<T>();

            try
            {
                IntPtr endIterator = Read<IntPtr>(mapAddress);
                IntPtr beginIteratorPtr = IntPtr.Add(mapAddress, 4);
                IntPtr currentIterator = Read<IntPtr>(beginIteratorPtr);

                if (currentIterator == endIterator)
                {
                    return values.ToArray();
                }

                int maxIterations = 10000;
                int iterations = 0;

                while (currentIterator != endIterator && iterations < maxIterations)
                {
                    try
                    {
                        IntPtr dataPtr = IntPtr.Add(currentIterator, dataOffset);
                        IntPtr actualDataPtr = Read<IntPtr>(dataPtr);

                        T value = extractValue(actualDataPtr);
                        values.Add(value);

                        currentIterator = GetNextMapIterator(currentIterator);
                        iterations++;
                    }
                    catch (Exception)
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Error reading std::map: {ex.Message}");
            }

            return values.ToArray();
        }

        /// <summary>
        /// Gets the next iterator in a std::map (simulates ++iterator)
        /// This is a simplified version - actual implementation depends on the specific std::map structure
        /// </summary>
        /// <param name="currentIterator">Current iterator position</param>
        /// <returns>Next iterator position</returns>
        private IntPtr GetNextMapIterator(IntPtr currentIterator)
        {
            // For MSVC std::map, the iterator structure typically has:
            // - Left child pointer at offset 0
            // - Parent pointer at offset 4
            // - Right child pointer at offset 8
            // - Color at offset 12 (red-black tree)
            // - Data at offset 16+

            // This is a simplified next() implementation for a red-black tree
            // Read the right child
            IntPtr rightChild = Read<IntPtr>(currentIterator);

            if (rightChild != IntPtr.Zero)
            {
                // If there's a right child, go to the leftmost node of the right subtree
                IntPtr current = rightChild;
                IntPtr left = Read<IntPtr>(IntPtr.Add(current, 8)); // Left child is typically at offset 8

                while (left != IntPtr.Zero)
                {
                    current = left;
                    left = Read<IntPtr>(IntPtr.Add(current, 8));
                }
                return current;
            }
            else
            {
                // If no right child, go up until we find a parent where current is a left child
                IntPtr parent = Read<IntPtr>(IntPtr.Add(currentIterator, 4)); // Parent at offset 4
                IntPtr current = currentIterator;

                while (parent != IntPtr.Zero)
                {
                    IntPtr parentRight = Read<IntPtr>(parent);
                    if (current != parentRight)
                        return parent;

                    current = parent;
                    parent = Read<IntPtr>(IntPtr.Add(parent, 4));
                }

                return IntPtr.Zero; // End of iteration
            }
        }

        /// <summary>
        /// Reads all m_pTarget values from a std::map based on the IDA pattern you provided
        /// This function mimics the behavior of the IDA code you showed
        /// </summary>
        /// <param name="mapAddress">Address of the MAP_FC0F20_AROUND_CHARS map</param>
        /// <returns>Array of m_pTarget values (IntPtr)</returns>
        public IntPtr[] ReadMapTargetValues(IntPtr mapAddress)
        {
            var targets = new List<IntPtr>();

            try
            {
                // Get the end iterator (MAP_FC0F20_AROUND_CHARS[0])
                IntPtr endIterator = Read<IntPtr>(mapAddress);

                // Initialize iterator (equivalent to sub_C162E0(&v3))
                IntPtr currentIterator = GetMapBeginIterator(mapAddress);

                int maxIterations = 10000; // Safety limit
                int iterations = 0;
                
                // Loop while currentIterator != endIterator (equivalent to v3 != MAP_FC0F20_AROUND_CHARS[0])
                while (currentIterator != endIterator && currentIterator != IntPtr.Zero && iterations < maxIterations)
                {
                    try
                    {
                        // Get the data pointer (equivalent to v3 + 20)
                        IntPtr dataPtr = IntPtr.Add(currentIterator, 20);
                        IntPtr actualData = Read<IntPtr>(dataPtr);
                       MessageBox.Show($"Data pointer: 0x{dataPtr.ToInt64():X8}, Actual data: 0x{actualData.ToInt64():X8}");
                        if (actualData != IntPtr.Zero)
                        {
                            // This is equivalent to *(_DWORD *)(v3 + 20) which gives us the target
                            targets.Add(actualData);
                            MessageBox.Show($"Found target: 0x{actualData.ToInt64():X8}");
                        }

                        // Move to next iterator (equivalent to sub_C10660(&v3))
                        currentIterator = GetNextMapIterator(currentIterator);
                        iterations++;
                    }
                    catch (Exception)
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Error reading map target values: {ex.Message}");
            }

            return targets.ToArray();
        }

        /// <summary>
        /// Gets the begin iterator for a std::map (simulates sub_C162E0(&v3))
        /// </summary>
        /// <param name="mapAddress">Address of the map</param>
        /// <returns>Begin iterator</returns>
        private IntPtr GetMapBeginIterator(IntPtr mapAddress)
        {
            try
            {
                // For MSVC std::map, the begin iterator is typically stored at offset 4
                IntPtr beginIteratorPtr = IntPtr.Add(mapAddress, 4);
                return Read<IntPtr>(beginIteratorPtr);
            }
            catch
            {
                return IntPtr.Zero;
            }
        }

        public AssemblyExecutor Assembly { get; private set; }
    }

    public class AssemblyExecutor
    {
        private readonly Memory memory;

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool VirtualFreeEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint dwFreeType);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateRemoteThread(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, out IntPtr lpThreadId);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern uint WaitForSingleObject(IntPtr hHandle, uint dwMilliseconds);

        [DllImport("kernel32.dll")]
        static extern bool GetThreadContext(IntPtr hThread, ref CONTEXT lpContext);

        public AssemblyExecutor(Memory memory)
        {
            this.memory = memory;
        }

        public T Execute<T>(IntPtr address, params object[] parameters) where T : struct
        {
            return Execute<T>(address, CallingConvention.Cdecl, parameters);
        }

        public T Execute<T>(IntPtr address, CallingConvention callingConvention, params object[] parameters) where T : struct
        {
            List<IntPtr> allocatedMemory = new List<IntPtr>();
            IntPtr asmAddress = IntPtr.Zero;
            IntPtr returnValueAddress = IntPtr.Zero;

            try
            {
                var processedParameters = new List<object>();
                foreach (var parameter in parameters.Reverse())
                {
                    if (parameter is string s)
                    {
                        byte[] stringBytes = Encoding.ASCII.GetBytes(s + "\0");
                        IntPtr stringAddress = VirtualAllocEx(memory.Process.Handle, IntPtr.Zero, (uint)stringBytes.Length, 0x1000, 0x40);
                        allocatedMemory.Add(stringAddress);
                        memory.WriteBytes(stringAddress, stringBytes);
                        processedParameters.Add(stringAddress);
                    }
                    else
                    {
                        processedParameters.Add(parameter);
                    }
                }

                returnValueAddress = VirtualAllocEx(memory.Process.Handle, IntPtr.Zero, (uint)Marshal.SizeOf(typeof(T)), 0x1000, 0x40);
                allocatedMemory.Add(returnValueAddress);

                byte[] asm = GenerateAsm(address, processedParameters, callingConvention, returnValueAddress);

                asmAddress = VirtualAllocEx(memory.Process.Handle, IntPtr.Zero, (uint)asm.Length, 0x1000, 0x40);
                memory.WriteBytes(asmAddress, asm);

                IntPtr threadHandle = CreateRemoteThread(memory.Process.Handle, IntPtr.Zero, 0, asmAddress, IntPtr.Zero, 0, out IntPtr threadId);
                WaitForSingleObject(threadHandle, 0xFFFFFFFF);

                return memory.Read<T>(returnValueAddress);
            }
            finally
            {
                if (asmAddress != IntPtr.Zero)
                {
                    VirtualFreeEx(memory.Process.Handle, asmAddress, 0, 0x8000);
                }
                foreach (var ptr in allocatedMemory)
                {
                    VirtualFreeEx(memory.Process.Handle, ptr, 0, 0x8000);
                }
            }
        }

        private byte[] GenerateAsm(IntPtr functionAddress, List<object> parameters, CallingConvention callingConvention, IntPtr returnValueAddress)
        {
            var asm = new List<byte>();

            foreach (var parameter in parameters)
            {
                asm.Add(0x68); // push
                if (parameter is IntPtr ptr)
                {
                    asm.AddRange(BitConverter.GetBytes(ptr.ToInt32()));
                }
                else if (parameter is int i)
                {
                    asm.AddRange(BitConverter.GetBytes(i));
                }
                else
                {
                    throw new ArgumentException("Unsupported parameter type: " + parameter.GetType());
                }
            }

            asm.Add(0xB8); // mov eax, address
            asm.AddRange(BitConverter.GetBytes(functionAddress.ToInt32()));

            asm.Add(0xFF); // call eax
            asm.Add(0xD0);

            // mov [returnValueAddress], eax
            asm.Add(0xA3);
            asm.AddRange(BitConverter.GetBytes(returnValueAddress.ToInt32()));

            if (callingConvention == CallingConvention.Cdecl)
            {
                asm.Add(0x83); // add esp, n
                asm.Add(0xC4);
                asm.Add((byte)(parameters.Count * 4));
            }

            asm.Add(0xC3); // ret

            return asm.ToArray();
        }

        [Flags]
        public enum CONTEXT_FLAGS : uint
        {
            CONTEXT_i386 = 0x10000,
            CONTEXT_i486 = 0x10000,   //  same as i386
            CONTEXT_CONTROL = (CONTEXT_i386 | 0x00000001), // SS:SP, CS:IP, EFLAGS, EBP
            CONTEXT_INTEGER = (CONTEXT_i386 | 0x00000002), // EAX, ECX, EDX, EBX, ESI, EDI
            CONTEXT_SEGMENTS = (CONTEXT_i386 | 0x00000004), // DS, ES, FS, GS
            CONTEXT_FLOATING_POINT = (CONTEXT_i386 | 0x00000008), // 387 state
            CONTEXT_DEBUG_REGISTERS = (CONTEXT_i386 | 0x00000010), // DB 0-3, 6, 7
            CONTEXT_EXTENDED_REGISTERS = (CONTEXT_i386 | 0x00000020), // cpu specific extensions
            CONTEXT_FULL = (CONTEXT_CONTROL | CONTEXT_INTEGER | CONTEXT_SEGMENTS), // all of the above
            CONTEXT_ALL = (CONTEXT_CONTROL | CONTEXT_INTEGER | CONTEXT_SEGMENTS | CONTEXT_FLOATING_POINT | CONTEXT_DEBUG_REGISTERS | CONTEXT_EXTENDED_REGISTERS)
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct CONTEXT
        {
            public CONTEXT_FLAGS ContextFlags; //set this to CONTEXT_CONTROL
            public uint Dr0;
            public uint Dr1;
            public uint Dr2;
            public uint Dr3;
            public uint Dr6;
            public uint Dr7;
            public FLOATING_SAVE_AREA FloatSave;
            public uint SegGs;
            public uint SegFs;
            public uint SegEs;
            public uint SegDs;
            public uint Edi;
            public uint Esi;
            public uint Ebx;
            public uint Edx;
            public uint Ecx;
            public uint Eax;
            public uint Ebp;
            public uint Eip;
            public uint SegCs;              // MUST BE SANITIZED
            public uint EFlags;             // MUST BE SANITIZED
            public uint Esp;
            public uint SegSs;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
            public byte[] ExtendedRegisters;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct FLOATING_SAVE_AREA
        {
            public uint ControlWord;
            public uint StatusWord;
            public uint TagWord;
            public uint ErrorOffset;
            public uint ErrorSelector;
            public uint DataOffset;
            public uint DataSelector;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 80)]
            public byte[] RegisterArea;
            public uint Cr0NpxState;
        }
    }
}