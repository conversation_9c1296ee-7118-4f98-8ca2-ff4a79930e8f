﻿namespace AIMemoryReader
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        private System.Windows.Forms.Label charNameLabel;
        private System.Windows.Forms.Button executeButton1;
        private System.Windows.Forms.Button executeButton2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Timer timer1;
        private System.Windows.Forms.Button getIpButton;
        private System.Windows.Forms.Label ipLabel;
        private System.Windows.Forms.Button getMapDataBTN;
        private System.Windows.Forms.RichTextBox targetsRichTextbox;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.charNameLabel = new System.Windows.Forms.Label();
            this.executeButton1 = new System.Windows.Forms.Button();
            this.executeButton2 = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.getIpButton = new System.Windows.Forms.Button();
            this.ipLabel = new System.Windows.Forms.Label();
            this.getMapDataBTN = new System.Windows.Forms.Button();
            this.targetsRichTextbox = new System.Windows.Forms.RichTextBox();
            this.SuspendLayout();
            // 
            // charNameLabel
            // 
            this.charNameLabel.AutoSize = true;
            this.charNameLabel.Location = new System.Drawing.Point(13, 109);
            this.charNameLabel.Name = "charNameLabel";
            this.charNameLabel.Size = new System.Drawing.Size(87, 13);
            this.charNameLabel.TabIndex = 0;
            this.charNameLabel.Text = "Character Name:";
            // 
            // executeButton1
            // 
            this.executeButton1.Location = new System.Drawing.Point(16, 40);
            this.executeButton1.Name = "executeButton1";
            this.executeButton1.Size = new System.Drawing.Size(75, 23);
            this.executeButton1.TabIndex = 1;
            this.executeButton1.Text = "IP";
            this.executeButton1.UseVisualStyleBackColor = true;
            this.executeButton1.Click += new System.EventHandler(this.executeButton1_Click);
            // 
            // executeButton2
            // 
            this.executeButton2.Location = new System.Drawing.Point(16, 70);
            this.executeButton2.Name = "executeButton2";
            this.executeButton2.Size = new System.Drawing.Size(75, 23);
            this.executeButton2.TabIndex = 2;
            this.executeButton2.Text = "Execute 2";
            this.executeButton2.UseVisualStyleBackColor = true;
            this.executeButton2.Click += new System.EventHandler(this.executeButton2_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(13, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(24, 13);
            this.label1.TabIndex = 0;
            this.label1.Text = "asd";
            // 
            // timer1
            // 
            this.timer1.Enabled = true;
            this.timer1.Interval = 2000;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // getIpButton
            // 
            this.getIpButton.Location = new System.Drawing.Point(16, 140);
            this.getIpButton.Name = "getIpButton";
            this.getIpButton.Size = new System.Drawing.Size(75, 23);
            this.getIpButton.TabIndex = 3;
            this.getIpButton.Text = "Get IP";
            this.getIpButton.UseVisualStyleBackColor = true;
            this.getIpButton.Click += new System.EventHandler(this.getIpButton_Click);
            // 
            // ipLabel
            //
            this.ipLabel.AutoSize = true;
            this.ipLabel.Location = new System.Drawing.Point(13, 170);
            this.ipLabel.Name = "ipLabel";
            this.ipLabel.Size = new System.Drawing.Size(0, 13);
            this.ipLabel.TabIndex = 4;
            //
            // getMapDataBTN
            //
            this.getMapDataBTN.Location = new System.Drawing.Point(16, 200);
            this.getMapDataBTN.Name = "getMapDataBTN";
            this.getMapDataBTN.Size = new System.Drawing.Size(100, 23);
            this.getMapDataBTN.TabIndex = 5;
            this.getMapDataBTN.Text = "Get Map Data";
            this.getMapDataBTN.UseVisualStyleBackColor = true;
            this.getMapDataBTN.Click += new System.EventHandler(this.getMapDataBTN_Click);
            //
            // targetsRichTextbox
            //
            this.targetsRichTextbox.Location = new System.Drawing.Point(130, 40);
            this.targetsRichTextbox.Name = "targetsRichTextbox";
            this.targetsRichTextbox.Size = new System.Drawing.Size(300, 200);
            this.targetsRichTextbox.TabIndex = 6;
            this.targetsRichTextbox.Text = "";
            // 
            // Form1
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(450, 280);
            this.Controls.Add(this.targetsRichTextbox);
            this.Controls.Add(this.getMapDataBTN);
            this.Controls.Add(this.getIpButton);
            this.Controls.Add(this.ipLabel);
            this.Controls.Add(this.executeButton2);
            this.Controls.Add(this.executeButton1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.charNameLabel);
            this.Name = "Form1";
            this.Text = "AI Memory Reader";
            this.Load += new System.EventHandler(this.Form1_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        
    }
}

