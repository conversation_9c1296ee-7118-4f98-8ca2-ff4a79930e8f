using System;
using System.Diagnostics;
using System.Net;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Linq;

namespace AIMemoryReader
{
    public class Network
    {
        // P/Invoke declarations for GetExtendedTcpTable
        [DllImport("iphlpapi.dll", SetLastError = true)]
        private static extern uint GetExtendedTcpTable(
            IntPtr pTcpTable,
            ref int dwOutBufLen,
            bool sort,
            int ipVersion,
            TCP_TABLE_CLASS tcpTableClass,
            uint reserved = 0
        );

        public enum TCP_TABLE_CLASS
        {
            TCP_TABLE_BASIC_LISTENER,
            TCP_TABLE_BASIC_CONNECTIONS,
            TCP_TABLE_BASIC_ALL,
            TCP_TABLE_OWNER_PID_LISTENER,
            TCP_TABLE_OWNER_PID_CONNECTIONS,
            TCP_TABLE_OWNER_PID_ALL,
            TCP_TABLE_OWNER_MODULE_LISTENER,
            TCP_TABLE_OWNER_MODULE_CONNECTIONS,
            TCP_TABLE_OWNER_MODULE_ALL
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct MIB_TCPTABLE_OWNER_PID
        {
            public uint dwNumEntries;
            [MarshalAs(UnmanagedType.ByValArray, ArraySubType = UnmanagedType.Struct, SizeConst = 1)]
            public MIB_TCPROW_OWNER_PID[] table;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct MIB_TCPROW_OWNER_PID
        {
            public uint dwState;
            public uint dwLocalAddr;
            public uint dwLocalPort;
            public uint dwRemoteAddr;
            public uint dwRemotePort;
            public uint dwOwningPid;
        }

        public static string GetIpAndPort(string processName)
        {
            try
            {
                Process[] processes = Process.GetProcessesByName(processName);
                if (processes.Length == 0)
                {
                    return "Process not found";
                }

                int targetProcessId = processes[0].Id;

                int bufferSize = 0;
                uint result = GetExtendedTcpTable(IntPtr.Zero, ref bufferSize, true, 2, TCP_TABLE_CLASS.TCP_TABLE_OWNER_PID_ALL);

                IntPtr tcpTablePtr = Marshal.AllocHGlobal(bufferSize);
                try
                {
                    result = GetExtendedTcpTable(tcpTablePtr, ref bufferSize, true, 2, TCP_TABLE_CLASS.TCP_TABLE_OWNER_PID_ALL);

                    if (result != 0)
                    {
                        return $"Error getting TCP table: {result}";
                    }

                    MIB_TCPTABLE_OWNER_PID tcpTable = (MIB_TCPTABLE_OWNER_PID)Marshal.PtrToStructure(tcpTablePtr, typeof(MIB_TCPTABLE_OWNER_PID));

                    for (int i = 0; i < tcpTable.dwNumEntries; i++)
                    {
                        MIB_TCPROW_OWNER_PID row = (MIB_TCPROW_OWNER_PID)Marshal.PtrToStructure(
                            new IntPtr(tcpTablePtr.ToInt64() + Marshal.SizeOf(typeof(uint)) + (i * Marshal.SizeOf(typeof(MIB_TCPROW_OWNER_PID)))),
                            typeof(MIB_TCPROW_OWNER_PID)
                        );

                        if (row.dwOwningPid == targetProcessId && (TcpState)row.dwState == TcpState.Established)
                        {
                            IPAddress remoteIp = new IPAddress(row.dwRemoteAddr);
                            int remotePort = (int)row.dwRemotePort;
                            return $"{remoteIp}:{IPAddress.NetworkToHostOrder((short)remotePort)}";
                        }
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(tcpTablePtr);
                }

                return "Not found";
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}";
            }
        }
    }
}