﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace AIMemoryReader
{
    public partial class Form1 : Form
    {
        private Memory memory;

        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                memory = new Memory("engineui.exe");
               // string charName = memory.ReadString((IntPtr)0x009C9754, 13, Encoding.ASCII);
               // charNameLabel.Text = "Character Name: " + charName;

                //int found = 0;
                //int found = memory.Assembly.Execute<int>((IntPtr)0x004286F0, CallingConvention.Cdecl, "stone of teleport");
                 //   label1.Text = found.ToString();
                
            }
            catch (ArgumentException ex)
            {
                MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }
        private void executeButton1_Click(object sender, EventArgs e)
        {
            //set zoom
            //memory.Write<float>((IntPtr)0x008A18BC, 1000);
            //memory.FindAndReplaceString("26.248.239.74", "127.0.0.1",Encoding.Default);

        }

        private void executeButton2_Click(object sender, EventArgs e)
        {
           // memory.Assembly.Execute<bool>((IntPtr)0x006DACC0, System.Runtime.InteropServices.CallingConvention.Cdecl, "s100001", 0, 0, 0, 0);
           // memory.Assembly.Execute<bool>((IntPtr)0x006E72C0, System.Runtime.InteropServices.CallingConvention.Cdecl, "*", "I'm Working", 0x34EB40);
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            //label1.Text = "searching";
            //int found = memory.Assembly.Execute<int>((IntPtr)0x004286F0, CallingConvention.Cdecl, "stone of teleport");
           // label1.Text = found.ToString();
        }

        private async void getIpButton_Click(object sender, EventArgs e)
        {
            //ipLabel.Text = "Loading...";
            //string ipAndPort = await Task.Run(() => Network.GetIpAndPort("engine"));
            //ipLabel.Text = ipAndPort;
        }

        private void getMapDataBTN_Click(object sender, EventArgs e)
        {
            try
            {
                if (memory == null)
                {
                    MessageBox.Show("Memory not initialized. Please restart the application.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Clear the textbox first
                targetsRichTextbox.Clear();
                targetsRichTextbox.AppendText("Reading map data...\n\n");
                targetsRichTextbox.Refresh();

                // Read the map data
                IntPtr mapAddress = new IntPtr(0x011FFF20);
                IntPtr[] targets = memory.ReadMapTargetValues(mapAddress);

                // Display the results
                targetsRichTextbox.Clear();
                targetsRichTextbox.AppendText($"Map Data Results (Found {targets.Length} entries):\n");
                targetsRichTextbox.AppendText("==========================================\n\n");

                if (targets.Length == 0)
                {
                    targetsRichTextbox.AppendText("No targets found in the map.\n");
                    targetsRichTextbox.AppendText("This could mean:\n");
                    targetsRichTextbox.AppendText("- The map is empty\n");
                    targetsRichTextbox.AppendText("- The map address (0xFC0F20) is incorrect\n");
                    targetsRichTextbox.AppendText("- The memory structure has changed\n");
                }
                else
                {
                    for (int i = 0; i < targets.Length; i++)
                    {
                        targetsRichTextbox.AppendText($"Target {i + 1}: 0x{targets[i].ToInt64():X8}\n");
                    }

                    targetsRichTextbox.AppendText($"\n==========================================\n");
                    targetsRichTextbox.AppendText($"Total targets found: {targets.Length}\n");
                }
            }
            catch (Exception ex)
            {
                targetsRichTextbox.Clear();
                targetsRichTextbox.AppendText($"Error reading map data:\n{ex.Message}\n\n");
                targetsRichTextbox.AppendText("Possible causes:\n");
                targetsRichTextbox.AppendText("- Process not found or not accessible\n");
                targetsRichTextbox.AppendText("- Invalid memory address\n");
                targetsRichTextbox.AppendText("- Memory protection issues\n");

                MessageBox.Show($"Error reading map data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}